import { globalPerformanceMonitor } from "./performance.js";

/**
 * Cache invalidation strategies
 */
export enum CacheInvalidationStrategy {
  TIME_BASED = "time-based",
  DEPENDENCY_BASED = "dependency-based",
  EVENT_BASED = "event-based",
  MANUAL = "manual",
}

/**
 * Cache dependency tracking
 */
export interface CacheDependency {
  type: "file" | "directory" | "project" | "command";
  path: string;
  lastModified?: Date;
  checksum?: string;
}

/**
 * Cache warming configuration
 */
export interface CacheWarmingConfig {
  enabled: boolean;
  strategies: string[];
  interval: number; // milliseconds
  maxConcurrency: number;
}

/**
 * Cache configuration options
 */
export interface CacheOptions {
  defaultTtl?: number;
  maxSize?: number;
  cleanupInterval?: number;
  enableStats?: boolean;
  enableAdvancedFeatures?: boolean;
  warmingConfig?: CacheWarmingConfig;
}

/**
 * Cache statistics for monitoring
 */
export interface CacheStats {
  hits: number;
  misses: number;
  entries: number;
  hitRate: number;
  memoryUsage: number;
  evictions?: number;
  averageAccessTime?: number;
}

/**
 * Basic cache entry
 */
interface BasicCacheEntry<T> {
  value: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

/**
 * Advanced cache entry with dependency tracking
 */
interface AdvancedCacheEntry<T> extends BasicCacheEntry<T> {
  dependencies: CacheDependency[];
  tags: string[];
  metadata: Record<string, unknown>;
}

/**
 * Unified cache implementation supporting both basic and advanced features
 */
export class UnifiedCache<T = unknown> {
  private cache = new Map<string, BasicCacheEntry<T> | AdvancedCacheEntry<T>>();
  private dependencyMap = new Map<string, Set<string>>();
  private tagMap = new Map<string, Set<string>>();
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
  };

  private readonly defaultTtl: number;
  private readonly maxSize: number;
  private readonly enableStats: boolean;
  private readonly enableAdvancedFeatures: boolean;
  private cleanupTimer?: NodeJS.Timeout;
  private accessTimes: number[] = [];

  constructor(options: CacheOptions = {}) {
    this.defaultTtl = options.defaultTtl ?? 300000; // 5 minutes default
    this.maxSize = options.maxSize ?? 1000;
    this.enableStats = options.enableStats ?? true;
    this.enableAdvancedFeatures = options.enableAdvancedFeatures ?? false;

    // Start cleanup timer
    const cleanupInterval = options.cleanupInterval ?? 60000; // 1 minute default
    this.cleanupTimer = setInterval(() => this.cleanup(), cleanupInterval);

    // Initialize cache warming if enabled
    if (options.warmingConfig?.enabled) {
      this.startCacheWarming(options.warmingConfig);
    }
  }

  /**
   * Get a value from the cache
   */
  async get(key: string): Promise<T | undefined> {
    const startTime = performance.now();

    try {
      const entry = this.cache.get(key);

      if (!entry) {
        if (this.enableStats) this.stats.misses++;
        return undefined;
      }

      // Check if entry has expired
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        this.cleanupEntryReferences(key);
        if (this.enableStats) {
          this.stats.misses++;
          this.stats.evictions++;
        }
        return undefined;
      }

      // Check dependencies for advanced entries
      if (this.enableAdvancedFeatures && this.isAdvancedEntry(entry)) {
        if (await this.areDependenciesInvalid(entry.dependencies)) {
          this.cache.delete(key);
          this.cleanupEntryReferences(key);
          if (this.enableStats) {
            this.stats.misses++;
            this.stats.evictions++;
          }
          return undefined;
        }
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = Date.now();

      if (this.enableStats) this.stats.hits++;
      return entry.value;
    } finally {
      if (this.enableStats) {
        const duration = performance.now() - startTime;
        this.accessTimes.push(duration);
        if (this.accessTimes.length > 1000) {
          this.accessTimes = this.accessTimes.slice(-500); // Keep last 500 measurements
        }
      }
    }
  }

  /**
   * Set a value in the cache (basic mode)
   */
  set(key: string, value: T, ttl?: number): void {
    this.setAdvanced(key, value, { ttl });
  }

  /**
   * Set a value in the cache with advanced options
   */
  setAdvanced(
    key: string,
    value: T,
    options: {
      ttl?: number;
      dependencies?: CacheDependency[];
      tags?: string[];
      metadata?: Record<string, unknown>;
    } = {}
  ): void {
    const {
      ttl = this.defaultTtl,
      dependencies = [],
      tags = [],
      metadata = {},
    } = options;

    const now = Date.now();

    // If cache is at max size, remove least recently used entry
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    let entry: BasicCacheEntry<T> | AdvancedCacheEntry<T>;

    if (
      this.enableAdvancedFeatures &&
      (dependencies.length > 0 ||
        tags.length > 0 ||
        Object.keys(metadata).length > 0)
    ) {
      entry = {
        value,
        timestamp: now,
        ttl,
        accessCount: 0,
        lastAccessed: now,
        dependencies,
        tags,
        metadata,
      } as AdvancedCacheEntry<T>;

      this.updateDependencyMap(key, dependencies);
      this.updateTagMap(key, tags);
    } else {
      entry = {
        value,
        timestamp: now,
        ttl,
        accessCount: 0,
        lastAccessed: now,
      } as BasicCacheEntry<T>;
    }

    this.cache.set(key, entry);
  }

  /**
   * Check if a key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.cleanupEntryReferences(key);
      return false;
    }

    return true;
  }

  /**
   * Delete a key from the cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.cleanupEntryReferences(key);
    }
    return deleted;
  }

  /**
   * Clear all entries from the cache
   */
  clear(): void {
    this.cache.clear();
    this.dependencyMap.clear();
    this.tagMap.clear();
    if (this.enableStats) {
      this.stats.hits = 0;
      this.stats.misses = 0;
      this.stats.evictions = 0;
    }
    this.accessTimes = [];
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? this.stats.hits / totalRequests : 0;
    const averageAccessTime =
      this.accessTimes.length > 0
        ? this.accessTimes.reduce((a, b) => a + b, 0) / this.accessTimes.length
        : 0;

    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      entries: this.cache.size,
      hitRate,
      memoryUsage: this.estimateMemoryUsage(),
      evictions: this.stats.evictions,
      averageAccessTime,
    };
  }

  /**
   * Get or set a value using a factory function
   */
  async getOrSet<R extends T>(
    key: string,
    factory: () => Promise<R>,
    options?: {
      ttl?: number;
      dependencies?: CacheDependency[];
      tags?: string[];
      metadata?: Record<string, unknown>;
    }
  ): Promise<R> {
    const cached = (await this.get(key)) as R;
    if (cached !== undefined) {
      return cached;
    }

    const value = await factory();
    this.setAdvanced(key, value, options);
    return value;
  }

  /**
   * Invalidate cache entries by dependency (advanced feature)
   */
  async invalidateByDependency(dependency: CacheDependency): Promise<void> {
    if (!this.enableAdvancedFeatures) return;

    const dependencyKey = this.getDependencyKey(dependency);
    const affectedKeys = this.dependencyMap.get(dependencyKey);

    if (affectedKeys) {
      for (const key of affectedKeys) {
        this.cache.delete(key);
        this.cleanupEntryReferences(key);
        if (this.enableStats) this.stats.evictions++;
      }
    }
  }

  /**
   * Invalidate cache entries by tag (advanced feature)
   */
  async invalidateByTag(tag: string): Promise<void> {
    if (!this.enableAdvancedFeatures) return;

    const affectedKeys = this.tagMap.get(tag);

    if (affectedKeys) {
      for (const key of affectedKeys) {
        this.cache.delete(key);
        this.cleanupEntryReferences(key);
        if (this.enableStats) this.stats.evictions++;
      }
    }
  }

  /**
   * Get all keys in the cache
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get the size of the cache
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Destroy the cache and cleanup resources
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    this.clear();
  }

  // Private helper methods

  private isAdvancedEntry(
    entry: BasicCacheEntry<T> | AdvancedCacheEntry<T>
  ): entry is AdvancedCacheEntry<T> {
    return "dependencies" in entry;
  }

  private isExpired(
    entry: BasicCacheEntry<T> | AdvancedCacheEntry<T>,
    now = Date.now()
  ): boolean {
    return now - entry.timestamp > entry.ttl;
  }

  private async areDependenciesInvalid(
    dependencies: CacheDependency[]
  ): Promise<boolean> {
    for (const dependency of dependencies) {
      if (await this.isDependencyInvalid(dependency)) {
        return true;
      }
    }
    return false;
  }

  private async isDependencyInvalid(
    dependency: CacheDependency
  ): Promise<boolean> {
    try {
      const fs = await import("fs/promises");
      const stats = await fs.stat(dependency.path);

      if (dependency.lastModified) {
        return stats.mtime > dependency.lastModified;
      }

      return false;
    } catch (error) {
      // If file doesn't exist, dependency is invalid
      return true;
    }
  }

  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.cleanupEntryReferences(oldestKey);
      if (this.enableStats) this.stats.evictions++;
    }
  }

  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry, now)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach((key) => {
      this.cache.delete(key);
      this.cleanupEntryReferences(key);
    });
  }

  private updateDependencyMap(
    key: string,
    dependencies: CacheDependency[]
  ): void {
    for (const dependency of dependencies) {
      const depKey = this.getDependencyKey(dependency);
      if (!this.dependencyMap.has(depKey)) {
        this.dependencyMap.set(depKey, new Set());
      }
      this.dependencyMap.get(depKey)!.add(key);
    }
  }

  private updateTagMap(key: string, tags: string[]): void {
    for (const tag of tags) {
      if (!this.tagMap.has(tag)) {
        this.tagMap.set(tag, new Set());
      }
      this.tagMap.get(tag)!.add(key);
    }
  }

  private cleanupEntryReferences(key: string): void {
    // Remove from dependency map
    for (const [depKey, keys] of this.dependencyMap.entries()) {
      keys.delete(key);
      if (keys.size === 0) {
        this.dependencyMap.delete(depKey);
      }
    }

    // Remove from tag map
    for (const [tag, keys] of this.tagMap.entries()) {
      keys.delete(key);
      if (keys.size === 0) {
        this.tagMap.delete(tag);
      }
    }
  }

  private getDependencyKey(dependency: CacheDependency): string {
    return `${dependency.type}:${dependency.path}`;
  }

  private estimateMemoryUsage(): number {
    let size = 0;

    for (const [key, entry] of this.cache.entries()) {
      // Rough estimation: key size + value size + entry overhead
      size += key.length * 2; // UTF-16 characters
      size += this.estimateValueSize(entry.value);
      size += 64; // Estimated overhead for entry object
    }

    return size;
  }

  private estimateValueSize(value: T): number {
    if (value === null || value === undefined) return 0;
    if (typeof value === "string") return value.length * 2;
    if (typeof value === "number") return 8;
    if (typeof value === "boolean") return 4;
    if (typeof value === "object") {
      try {
        return JSON.stringify(value).length * 2;
      } catch {
        return 100; // Fallback estimate
      }
    }
    return 50; // Default estimate
  }

  private startCacheWarming(config: CacheWarmingConfig): void {
    setInterval(() => {
      // Implement cache warming logic here
      // This would be customized based on application needs
    }, config.interval);
  }
}

/**
 * Global cache manager for creating and managing named cache instances
 */
export class CacheManager {
  private static instances = new Map<string, UnifiedCache>();

  /**
   * Get or create a named cache instance
   */
  static getCache<T = unknown>(
    name: string,
    options?: CacheOptions
  ): UnifiedCache<T> {
    if (!this.instances.has(name)) {
      this.instances.set(name, new UnifiedCache<T>(options));
    }
    return this.instances.get(name) as UnifiedCache<T>;
  }

  /**
   * Destroy a named cache instance
   */
  static destroyCache(name: string): boolean {
    const cache = this.instances.get(name);
    if (cache) {
      cache.destroy();
      return this.instances.delete(name);
    }
    return false;
  }

  /**
   * Destroy all cache instances
   */
  static destroyAll(): void {
    for (const cache of this.instances.values()) {
      cache.destroy();
    }
    this.instances.clear();
  }

  /**
   * Get statistics for all caches
   */
  static getAllStats(): Record<string, CacheStats> {
    const stats: Record<string, CacheStats> = {};
    for (const [name, cache] of this.instances.entries()) {
      stats[name] = cache.getStats();
    }
    return stats;
  }

  /**
   * Get global cache statistics
   */
  static getGlobalStatistics(): {
    totalHits: number;
    totalMisses: number;
    totalEvictions: number;
    totalMemoryUsage: number;
    cacheCount: number;
    individualCaches: Record<string, CacheStats>;
  } {
    const individualCaches: Record<string, CacheStats> = {};
    let totalHits = 0;
    let totalMisses = 0;
    let totalEvictions = 0;
    let totalMemoryUsage = 0;

    for (const [name, cache] of this.instances.entries()) {
      const stats = cache.getStats();
      individualCaches[name] = stats;
      totalHits += stats.hits;
      totalMisses += stats.misses;
      totalEvictions += stats.evictions || 0;
      totalMemoryUsage += stats.memoryUsage;
    }

    return {
      totalHits,
      totalMisses,
      totalEvictions,
      totalMemoryUsage,
      cacheCount: this.instances.size,
      individualCaches,
    };
  }

  /**
   * Clear all caches
   */
  static clearAll(): void {
    for (const cache of this.instances.values()) {
      cache.clear();
    }
  }
}

// Pre-configured cache instances for common use cases
export const projectCache = CacheManager.getCache("projects", {
  defaultTtl: 300000, // 5 minutes
  maxSize: 100,
});

export const fileCache = CacheManager.getCache("files", {
  defaultTtl: 60000, // 1 minute
  maxSize: 500,
});

export const commandCache = CacheManager.getCache("commands", {
  defaultTtl: 30000, // 30 seconds
  maxSize: 200,
});

// Backward compatibility exports
export const TimeBasedCache = UnifiedCache;
export const AdvancedCacheManager = CacheManager;

// Legacy type aliases for backward compatibility
export type { CacheStats as CacheStatistics };
export type { UnifiedCache as AdvancedCache };
